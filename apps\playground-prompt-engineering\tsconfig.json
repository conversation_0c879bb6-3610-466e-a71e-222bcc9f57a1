{"extends": "../../tsconfig.json", "compilerOptions": {"jsx": "preserve", "lib": ["DOM", "ESNext", "DOM.Iterable", "DOM.AsyncIterable"], "paths": {"@proj-airi/stage-ui/*": ["../../packages/stage-ui/src/*"]}, "resolveJsonModule": true, "types": ["vitest", "vite/client", "vite-plugin-vue-layouts/client", "unplugin-vue-macros/macros-global", "unplugin-vue-router/client"], "allowJs": true, "strict": true, "skipLibCheck": true}, "vueCompilerOptions": {"plugins": ["@vue-macros/volar/define-models", "@vue-macros/volar/define-slots"]}}