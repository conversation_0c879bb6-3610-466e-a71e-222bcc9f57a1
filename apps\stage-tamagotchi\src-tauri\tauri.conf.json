{"$schema": "../node_modules/@tauri-apps/cli/config.schema.json", "productName": "AIRI", "version": "../package.json", "identifier": "ai.moeru.airi-tamagotchi", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:5173", "beforeDevCommand": "pnpm run dev", "beforeBuildCommand": "pnpm run build"}, "app": {"security": {"csp": null}, "macOSPrivateApi": true}, "bundle": {"active": true, "targets": ["dmg", "appimage", "nsis"], "icon": ["icons/32x32.png", "icons/64x64.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": {"infoplist/**/*": "./"}, "macOS": {"entitlements": "./Entitlements.plist"}}}