name: HuggingFace Spaces

on:
  push:
    branches:
      - main

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          lfs: true

      - run: |-
          git clone https://$HF_USERNAME:$<EMAIL>/spaces/moeru-ai/airi --depth 1 apps/stage-web/dist
        env:
          HF_TOKEN: ${{ secrets.HF_TOKEN }}
          HF_USERNAME: ${{ secrets.HF_USERNAME }}

      - uses: pnpm/action-setup@v3
      - uses: actions/setup-node@v4
        with:
          node-version: lts/*
          cache: pnpm

      - run: pnpm install
      - run: pnpm build
        env:
          TARGET_HUGGINGFACE_SPACE: 'true'
          VITE_APP_TARGET_HUGGINGFACE_SPACE: 'true'

      - id: airi_diff
        working-directory: ./apps/stage-web/dist
        run: |-
          git lfs ls-files --all
          git add .
          if [[ -n $(git status --porcelain) ]]; then
              echo "changes=true" >> "$GITHUB_OUTPUT";
          fi

      - if: steps.airi_diff.outputs.changes == 'true'
        working-directory: ./apps/stage-web/dist
        env:
          HF_TOKEN: ${{ secrets.HF_TOKEN }}
          HF_USERNAME: ${{ secrets.HF_USERNAME }}
        run: |-
          git config --local user.email "<EMAIL>"
          git config --local user.name "Neko Ayaka"
          git commit -m "release: build ${{ github.sha }}"
          git lfs push origin main --all
          git push -f
