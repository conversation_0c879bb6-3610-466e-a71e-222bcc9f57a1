{"compilerOptions": {"target": "ESNext", "lib": ["ESNext", "DOM", "DOM.Iterable", "DOM.AsyncIterable", "WebWorker"], "module": "ESNext", "moduleResolution": "bundler", "types": ["vite/client", "unplugin-vue-router/client"], "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "verbatimModuleSyntax": true, "skipLibCheck": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.mts"]}