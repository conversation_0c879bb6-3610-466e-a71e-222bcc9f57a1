<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>AIRI</title>
    <meta name="description" content="Open source attempt to create cyber companion">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" sizes="any" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <meta name="theme-color" content="rgb(18,18,18)" id="themeColor" />
    <meta name="apple-mobile-web-app-title" content="AIRI" />
    <script>
      ;(function () {
        const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        const setting = localStorage.getItem('vueuse-color-scheme') || 'auto'
        if (setting === 'light' || (prefersDark && setting !== 'dark'))
          document.querySelector('#themeColor')?.setAttribute('content', 'rgb(255,255,255)')
      })()
    </script>
    <script>
      ;(function () {
        const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        const setting = localStorage.getItem('vueuse-color-scheme') || 'auto'
        if (setting === 'dark' || (prefersDark && setting !== 'light'))
          document.documentElement.classList.toggle('dark', true)
      })()
    </script>
    <script
      defer
      data-domain="airi.moeru.ai"
      data-api="/api/v1/page-external-data/submit"
      src="/remote-assets/page-external-data/js/script.js"
    ></script>
    <script src="/assets/js/CubismSdkForWeb-5-r.3/Core/live2dcubismcore.min.js"></script>
  </head>
  <body class="font-sans">
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
    <noscript> This website requires JavaScript to function properly. Please enable JavaScript to continue. </noscript>
  </body>
</html>
