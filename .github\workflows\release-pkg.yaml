name: Release

on:
  push:
    tags:
      - 'v*'

env:
  STORE_PATH: ''

permissions:
  contents: write
  issues: write
  pull-requests: write
  id-token: write
  packages: write

jobs:
  release:
    name: Release
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: pnpm/action-setup@v3
        name: Install pnpm
        with:
          run_install: false

      - name: Install Node.js 24.x
        uses: actions/setup-node@v4
        with:
          node-version: 24.x
          # registry-url required. Learn more at
          # https://docs.github.com/en/actions/publishing-packages/publishing-nodejs-packages
          registry-url: 'https://registry.npmjs.org'
          cache: pnpm

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Packages build
        run: pnpm run build:packages

      - name: Packages publish (dry run)
        run: pnpm publish -r --access public --no-git-checks --dry-run
        env:
          NODE_AUTH_TOKEN: ${{secrets.NPM_TOKEN}}
          NPM_TOKEN: ${{secrets.NPM_TOKEN}}
          NPM_CONFIG_PROVENANCE: true

      - name: Packages publish
        run: pnpm publish -r --access public --no-git-checks
        env:
          NODE_AUTH_TOKEN: ${{secrets.NPM_TOKEN}}
          NPM_TOKEN: ${{secrets.NPM_TOKEN}}
          NPM_CONFIG_PROVENANCE: true
