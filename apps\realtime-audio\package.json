{"name": "@proj-airi/realtime-audio", "type": "module", "private": true, "description": "Realtime audio", "author": {"name": "Moeru AI Project AIRI Team", "email": "<EMAIL>", "url": "https://github.com/moeru-ai"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/moeru-ai/airi.git", "directory": "apps/realtime-audio"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "typecheck": "vue-tsc --noEmit"}, "dependencies": {"@vueuse/core": "^13.6.0", "@xsai/generate-text": "catalog:", "@xsai/shared-chat": "catalog:", "@xsai/stream-text": "catalog:", "es-toolkit": "^1.39.8", "vue": "^3.5.18"}, "devDependencies": {"@iconify-json/solar": "^1.2.2", "@unocss/reset": "^66.3.3", "@vitejs/plugin-vue": "^6.0.1", "superjson": "^2.2.2", "unplugin-vue-router": "^0.14.0", "vite": "catalog:rolldown-vite", "vue-router": "^4.5.1", "vue-tsc": "^3.0.5"}}