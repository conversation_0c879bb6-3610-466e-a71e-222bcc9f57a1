{"name": "@proj-airi/component-calling", "type": "module", "private": true, "description": "Realtime audio", "author": {"name": "Moeru AI Project AIRI Team", "email": "<EMAIL>", "url": "https://github.com/moeru-ai"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/moeru-ai/airi.git", "directory": "apps/component-calling"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "typecheck": "vue-tsc --noEmit"}, "dependencies": {"@proj-airi/ui": "workspace:^", "@valibot/to-json-schema": "1.0.0-rc.0", "@vueuse/core": "^13.6.0", "@xsai/generate-text": "catalog:", "@xsai/shared-chat": "catalog:", "@xsai/stream-text": "catalog:", "es-toolkit": "^1.39.8", "valibot": "1.0.0-beta.9", "vue": "^3.5.18", "xsschema": "catalog:"}, "devDependencies": {"@iconify-json/solar": "^1.2.2", "@iconify-json/svg-spinners": "^1.2.2", "@types/xast": "^2.0.4", "@unocss/reset": "^66.3.3", "@vitejs/plugin-vue": "^6.0.1", "superjson": "^2.2.2", "unplugin-vue-router": "^0.14.0", "vite": "catalog:rolldown-vite", "vue-router": "^4.5.1", "vue-tsc": "^3.0.5"}}