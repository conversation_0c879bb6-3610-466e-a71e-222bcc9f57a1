// noinspection ES6PreferShortImport: IntelliJ IDE hint to avoid warning to use `~/contributors`, will fail on build if changed

/* Texts */
export const rekaName = 'Project AIRI'
export const rekaShortName = 'AIRI'
export const rekaDescription
  = 'An open-source attempt to re-create <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, the most famous AI VTuber on this earth, but also forming a cyber companion for everyone.'

/* vitepress head */
export const ogUrl = 'https://airi.moeru.ai/'
export const ogImage = `${ogUrl}og.jpg`

/* GitHub and social links */
export const github = 'https://github.com/moeru-ai/airi'
export const releases = 'https://github.com/moeru-ai/airi/releases'
export const contributing = 'https://github.com/moeru-ai/airi/blob/main/.github/CONTRIBUTING.md'
export const discord = 'https://discord.gg/TgQ3Cu2F7A'
export const x = 'https://x.com/proj_airi'

/* Avatar/Image/Sponsors servers */
export const sponsor = 'https://github.com/sponsors/nekomeowww'
