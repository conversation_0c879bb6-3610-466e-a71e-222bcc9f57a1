<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Project AIRI Real-time Audio Playground</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0" />
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <script>
      ;(function () {
        const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
        const setting = localStorage.getItem('vueuse-color-scheme') || 'auto'
        if (setting === 'dark' || (prefersDark && setting !== 'light'))
          document.documentElement.classList.toggle('dark', true)
      })()
    </script>
  </head>
  <body class="font-sans">
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
    <noscript> This website requires JavaScript to function properly. Please enable JavaScript to continue. </noscript>
  </body>
</html>
