{"name": "@proj-airi/playground-prompt-engineering", "type": "module", "version": "0.7.1", "private": true, "description": "Prompt Engineering Playground for AI Characters", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "typecheck": "vue-tsc --noEmit"}, "dependencies": {"@velin-dev/core": "^0.2.6", "@velin-dev/vue": "^0.2.6", "pinia": "^3.0.3", "vue": "^3.5.18"}, "devDependencies": {"@iconify/vue": "^5.0.0", "@unocss/reset": "^66.3.3", "@vitejs/plugin-vue": "^6.0.1", "typescript": "~5.9.2", "unocss": "^66.3.3", "unplugin-vue-macros": "^2.14.5", "unplugin-vue-router": "^0.14.0", "vite": "catalog:rolldown-vite", "vite-plugin-vue-devtools": "^8.0.0", "vite-plugin-vue-layouts": "^0.11.0", "vue-tsc": "^3.0.5"}}