version: "0.2"
ignorePaths: []
dictionaryDefinitions: []
dictionaries: []
words:
  - acubismmotion
  - Aerisita
  - airi
  - airi-vtuber
  - Alaya
  - alexanderolsen
  - alibabacloud
  - aliyun
  - allseto
  - animejs
  - APNG
  - astrojs
  - Attributify
  - attw
  - audioworklet
  - autoplay
  - awilix
  - Ayaka
  - baichuan
  - baiducloud
  - bailian
  - bigserial
  - Bitstream
  - browserbasehq
  - buildless
  - bumpp
  - byteorder
  - catppuccin
  - cdylib
  - cientos
  - cjkfonts
  - clippy
  - clustr
  - collectblock
  - colorjs
  - Comfortaa
  - composables
  - cooldown
  - coreml
  - cosyvoice
  - cozyvoice
  - crossws
  - csmmap
  - csmvector
  - csstype
  - cubismbreath
  - cubismdebug
  - cubismdefaultparameterid
  - cubismeyeblink
  - cubismmatrix44
  - cubismmoc
  - cubismmodelsettingjson
  - cubismmotionqueuemanager
  - cubismusermodel
  - cubismviewmatrix
  - cuda
  - culori
  - cuteen
  - dataurl
  - deepseek
  - defu
  - demi
  - demodel
  - devlog
  - devlogs
  - directml
  - dokidoki
  - dotenvx
  - DownloadLive2DSDK
  - dreamlog
  - dtolnay
  - dtype
  - dtypes
  - duckdb
  - DuckDBWASM
  - DuckDBWASMQ
  - DuckDBWASMQuery
  - eastasia
  - elevenlabs
  - Equirectangular
  - esaxx
  - Factorio
  - feaxios
  - formkit
  - frontmatter
  - giteeai
  - gltf
  - gpuu
  - grammyjs
  - groq
  - gugi
  - guii
  - guiiai
  - Hdri
  - hfspaceup
  - hfsup
  - hfup
  - higress
  - histoire
  - hiyori
  - Holo
  - huggingface
  - huggingspace
  - hunyuan
  - hyoban
  - iconify
  - intlify
  - jlumbroso
  - jszip
  - Kawaii
  - kebabcase
  - keyup
  - Keyyable
  - kirakira
  - kwaa
  - lemonnekogh
  - lerp
  - libsamplerate
  - libsodium
  - lightningcss
  - listhen
  - live2dcubismcore
  - live2dcubismframework
  - Llmmarker
  - lobehub
  - logg
  - Lorebook
  - lucide
  - luoling
  - Maekawa
  - magnifer
  - Makito
  - Maru
  - matchall
  - mdit
  - micvad
  - mineflayer
  - mingcute
  - minimizable
  - minisearch
  - mkdist
  - modelcontextprotocol
  - modnet
  - moeru
  - Morioki
  - mousedown
  - mouseup
  - msvc
  - Myriam
  - ndarray
  - Neko
  - nekomeowww
  - neuri
  - Neuro
  - Neuro-sama
  - nolyfill
  - nomic
  - novita
  - nprogress
  - nuxi
  - nuxt
  - nuxtjs
  - objc
  - ofetch
  - oklch
  - ollama
  - Oneliner
  - onnx
  - onnxruntime
  - openai
  - openrouter
  - opentype
  - OPFS
  - opusscript
  - pglite
  - pgvector
  - picklist
  - picovoice
  - pinia
  - pixi
  - pixiv
  - popmotion
  - poppin
  - PREFLIGHTS
  - prereleased
  - pretrained
  - prismarine
  - prng
  - pthread
  - quanlai
  - qwen
  - Raycaster
  - rdev
  - rehype
  - reka
  - Replayable
  - resampler
  - RGBE
  - rlib
  - rmcp
  - rolldown
  - rushstack
  - Rynco
  - Saccade
  - saccades
  - safetensors
  - SAVEPOINT
  - sensenova
  - serde
  - Shadcn
  - shiki
  - shikijs
  - silero
  - siliconcloud
  - sizecheck
  - smallserial
  - Sniglet
  - sonner
  - specta
  - splt
  - srgb
  - ssml
  - staticlib
  - stepfun
  - supergroup
  - superjson
  - tamagotchi
  - tauri
  - taze
  - togetherapi
  - tolist
  - tresjs
  - tsdown
  - turborepo
  - unbundle
  - uncrypto
  - unhead
  - Unlisten
  - unocss
  - unplugin
  - unspeech
  - valibot
  - vaul
  - velin
  - VITE
  - vitepress
  - vllm
  - Volcengine
  - vrma
  - vtuber
  - vueuse
  - waapi
  - wavefile
  - webgpu
  - weeb
  - wgpu
  - wlipsync
  - worklet
  - worklets
  - xast
  - xastscript
  - Xenova
  - xiaolai
  - xsai
  - xsschema
  - zhipu
ignoreWords:
  - smol-toml
import: []
